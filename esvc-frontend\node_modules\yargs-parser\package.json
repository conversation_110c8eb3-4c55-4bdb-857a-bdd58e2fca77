{"name": "yargs-parser", "version": "18.1.3", "description": "the mighty option parser used by yargs", "main": "index.js", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "posttest": "standard", "coverage": "c8 report --check-coverage check-coverage --lines=100 --branches=97 --statements=100"}, "repository": {"type": "git", "url": "https://github.com/yargs/yargs-parser.git"}, "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1"}, "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "files": ["lib", "index.js"], "engines": {"node": ">=6"}}