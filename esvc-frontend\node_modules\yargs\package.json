{"name": "yargs", "version": "15.4.1", "description": "yargs the modern, pirate-themed, successor to optimist.", "main": "./index.js", "contributors": [{"name": "Yargs Contributors", "url": "https://github.com/yargs/yargs/graphs/contributors"}], "files": ["index.js", "yargs.js", "build", "locales", "LICENSE"], "dependencies": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.2"}, "devDependencies": {"@types/chai": "^4.2.11", "@types/decamelize": "^1.2.0", "@types/mocha": "^7.0.2", "@types/node": "^10.0.3", "@typescript-eslint/eslint-plugin": "^3.0.0", "@typescript-eslint/parser": "^3.0.0", "c8": "^7.0.0", "chai": "^4.2.0", "chalk": "^4.0.0", "coveralls": "^3.0.9", "cpr": "^3.0.1", "cross-spawn": "^7.0.0", "es6-promise": "^4.2.5", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "gts": "^2.0.0-alpha.4", "hashish": "0.0.4", "mocha": "^7.0.0", "rimraf": "^3.0.2", "standardx": "^5.0.0", "typescript": "^3.7.0", "which": "^2.0.0", "yargs-test-extends": "^1.0.1"}, "scripts": {"fix": "standardx --fix  && standardx --fix **/*.ts", "posttest": "npm run check", "test": "c8 mocha --require ./test/before.js --timeout=12000 --check-leaks", "coverage": "c8 report --check-coverage", "check": "standardx && standardx **/*.ts", "compile": "rimraf build && tsc", "prepare": "npm run compile", "pretest": "npm run compile -- -p tsconfig.test.json"}, "repository": {"type": "git", "url": "https://github.com/yargs/yargs.git"}, "homepage": "https://yargs.js.org/", "standardx": {"ignore": ["build", "**/example/**"]}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "engines": {"node": ">=8"}}