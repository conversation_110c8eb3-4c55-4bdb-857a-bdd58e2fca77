import React, { useState } from 'react';
import '../../styles/components/user-dashboard/SecuritySettings.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';

// Import icons
import cardCoinIcon from '../../assets/card-coin.png';

// Lock SVG Component
const LockIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6 10V8C6 5.79086 7.79086 4 10 4H14C16.2091 4 18 5.79086 18 8V10H19C20.1046 10 21 10.8954 21 12V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V12C3 10.8954 3.89543 10 5 10H6ZM8 8V10H16V8C16 6.89543 15.1046 6 14 6H10C8.89543 6 8 6.89543 8 8Z" fill="currentColor"/>
  </svg>
);

// Key SVG Component
const KeyIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7 14C5.34315 14 4 12.6569 4 11C4 9.34315 5.34315 8 7 8C8.65685 8 10 9.34315 10 11C10 11.7403 9.59717 12.3866 9.00006 12.7324L11 14.7324V16H9.5V17.5H8V19H6.5V17.5H5V15.2676C4.40283 14.9218 4 14.2755 4 13.5336V11C4 9.34315 5.34315 8 7 8C8.65685 8 10 9.34315 10 11H8C8 10.4477 7.55228 10 7 10C6.44772 10 6 10.4477 6 11C6 11.5523 6.44772 12 7 12C7.55228 12 8 11.5523 8 11H10C10 12.6569 8.65685 14 7 14Z" fill="currentColor"/>
  </svg>
);

const SecuritySettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('security-settings');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  const handleChangePassword = () => {
    // Navigate to change password page
    window.location.href = '/user-dashboard/change-password';
  };

  const handleResetPassword = () => {
    // Navigate to reset password page
    window.location.href = '/user-dashboard/reset-password';
  };

  return (
    <UserDashboardLayout className="security-settings-container">
      <div className="security-settings-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosín 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>
          </div>
          
          <div className="header-controls">
            <button className="stake-esvc-btn">
              <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
              Stake ESVC
            </button>
            
            <div className="balance-toggle">
              <span className="toggle-label">Hide balances</span>
              <label className="toggle-switch">
                <input 
                  type="checkbox" 
                  checked={hideBalances}
                  onChange={toggleBalances}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* Dashboard Layout */}
        <div className="dashboard-layout">
          {/* User Side Navigation */}
          <UserSideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Security Settings Content */}
          <div className="security-settings-main">
            <div className="settings-header">
              <h2 className="settings-title">Security Settings</h2>
            </div>

            <div className="security-options">
              <div className="security-option" onClick={handleChangePassword}>
                <div className="option-icon">
                  <LockIcon />
                </div>
                <div className="option-content">
                  <h3 className="option-title">Change Password</h3>
                  <p className="option-description">Update your password to keep your account secure.</p>
                </div>
                <div className="option-arrow">→</div>
              </div>

              <div className="security-option" onClick={handleResetPassword}>
                <div className="option-icon">
                  <KeyIcon />
                </div>
                <div className="option-content">
                  <h3 className="option-title">Reset Password</h3>
                  <p className="option-description">Reset your password if you've forgotten it or need to recover your account.</p>
                </div>
                <div className="option-arrow">→</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default SecuritySettings;
